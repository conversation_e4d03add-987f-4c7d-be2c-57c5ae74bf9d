-- Core User Tables (managed by <PERSON>host auth)
-- auth.users is managed by Nhost, this is the public view
CREATE TABLE users (
    id UUID PRIMARY KEY, -- References auth.users(id)
    email TEXT NOT NULL,
    displayName TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
    -- Other standard Nhost auth fields
);

-- Wallet System
CREATE TABLE user_wallet (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_balance INTEGER DEFAULT 0,
    fast_credits_balance INTEGER DEFAULT 0,
    last_free_credit_date TIMESTAMPTZ,
    active_pass_id UUID REFERENCES user_pass(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE user_pass (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    pass_type TEXT NOT NULL, -- 'daily', 'monthly', 'annual', etc.
    status TEXT NOT NULL DEFAULT 'active',
    expires_at TIMESTAMPTZ,
    grants_fast_credits BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    monthly_price DECIMAL(10,2),
    provider_price_id TEXT,
    grants_fast_credits_monthly INTEGER DEFAULT 0,
    server_type_access TEXT NOT NULL, -- 'slow', 'fast', 'both'
    allows_overdraft BOOLEAN DEFAULT false,
    is_subscription BOOLEAN DEFAULT true,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE credit_bundles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    credits_amount INTEGER NOT NULL,
    provider_price_id TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    change_amount INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    fast_balance_after INTEGER,
    deducted_from TEXT, -- 'credits', 'fast_credits', 'overdraft'
    transaction_type TEXT NOT NULL, -- 'purchase', 'usage', 'grant', 'refund'
    related_id TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE active_subscription (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    current_period_end TIMESTAMPTZ,
    plan_id UUID REFERENCES plans(id),
    provider_subscription_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Generation System
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tool_id TEXT NOT NULL,
    input_file TEXT,
    user_prompt TEXT,
    server_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    project_id UUID REFERENCES projects(id),
    error_message TEXT,
    uploaded_file_id UUID REFERENCES storage.files(id),
    output_file TEXT,
    credit_cost INTEGER,
    retry_count INTEGER DEFAULT 0,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prompt_id UUID REFERENCES prompts(id),
    name TEXT,
    input_file_url TEXT,
    prompt_input_text TEXT,
    credit_cost DECIMAL(10,2),
    generation_duration_ms INTEGER,
    outputs JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    saved_at TIMESTAMPTZ DEFAULT NOW()
);

-- Configuration Tables
CREATE TABLE tools (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    workflow_file_id UUID REFERENCES storage.files(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE comfyui_servers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    server_type TEXT NOT NULL, -- 'slow', 'fast'
    is_active BOOLEAN DEFAULT true,
    tool_id TEXT, -- Specific tool or 'all' for general purpose
    queue_priority INTEGER DEFAULT 0,
    cost_credits_per_second DECIMAL(10,4),
    max_concurrent_jobs INTEGER DEFAULT 1,
    current_jobs INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);