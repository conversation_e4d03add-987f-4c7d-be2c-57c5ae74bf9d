-- Core User Tables (managed by <PERSON>host auth)
-- auth.users is managed by Nhost, this is the public view
CREATE TABLE users (
    id UUID PRIMARY KEY, -- References auth.users(id)
    email TEXT NOT NULL,
    displayName TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
    -- Other standard Nhost auth fields
);

-- Wallet & Pass System
CREATE TABLE user_wallet (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_balance INTEGER DEFAULT 0, -- Regular credits (for pay-as-you-go)
    fast_credits_balance INTEGER DEFAULT 0, -- Fast generation credits
    last_free_credit_grant TIMESTAMPTZ, -- For daily free credits
    active_pass_id UUID REFERENCES user_passes(id), -- Current active pass
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE pass_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- 'Visitor Free', 'Visitor Paid', 'Citizen Lite', 'Citizen Pro', 'Citizen Elite'
    tier TEXT NOT NULL, -- 'Free', 'Paid', 'Lite', 'Pro', 'Elite'
    category TEXT NOT NULL, -- 'Visitor', 'Citizen'
    monthly_price DECIMAL(10,2),
    hours_included INTEGER, -- NULL for unlimited
    is_unlimited BOOLEAN DEFAULT false,
    slow_generation_limit INTEGER, -- NULL for unlimited
    fast_generation_limit INTEGER, -- NULL for unlimited
    provider_price_id TEXT, -- For payment integration
    features JSONB, -- Additional features as JSON
    badge_image_url TEXT, -- For the gaming-style pass design
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE user_passes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    pass_type_id UUID NOT NULL REFERENCES pass_types(id),
    start_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_date TIMESTAMPTZ, -- NULL for lifetime passes
    status TEXT NOT NULL DEFAULT 'active', -- 'active', 'expired', 'cancelled'
    hours_used INTEGER DEFAULT 0, -- Track usage for limited passes
    provider_subscription_id TEXT, -- For subscription management
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE credit_bundles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- '$1 - 30 credits', etc.
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    credits_amount INTEGER NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    provider_price_id TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL, -- Positive for additions, negative for deductions
    transaction_type TEXT NOT NULL, -- 'purchase', 'usage', 'daily_grant', 'refund'
    balance_after INTEGER NOT NULL,
    fast_balance_after INTEGER,
    deduction_source TEXT, -- 'regular', 'fast', NULL for additions
    reference_id UUID, -- Can reference prompts, bundles, etc.
    bundle_id UUID REFERENCES credit_bundles(id),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Generation System
CREATE TABLE tools (
    id TEXT PRIMARY KEY, -- 'reimagine', 'img2video', etc.
    name TEXT NOT NULL,
    description TEXT,
    workflow_file_id UUID REFERENCES storage.files(id), -- ComfyUI workflow
    thumbnail_url TEXT,
    is_active BOOLEAN DEFAULT true,
    requires_input_image BOOLEAN DEFAULT true,
    requires_prompt BOOLEAN DEFAULT true,
    slow_credit_cost_per_second DECIMAL(10,4),
    fast_credit_cost_per_second DECIMAL(10,4),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE comfyui_servers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    server_type TEXT NOT NULL, -- 'slow', 'fast'
    is_active BOOLEAN DEFAULT true,
    tool_id TEXT, -- Specific tool or NULL for general purpose
    queue_priority INTEGER DEFAULT 0,
    max_concurrent_jobs INTEGER DEFAULT 1,
    current_jobs INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tool_id TEXT NOT NULL REFERENCES tools(id),
    input_file_name TEXT,
    input_file_id UUID REFERENCES storage.files(id),
    user_prompt TEXT,
    server_type TEXT NOT NULL, -- 'slow', 'fast'
    server_id UUID REFERENCES comfyui_servers(id),
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    comfy_prompt_id TEXT, -- ID returned by ComfyUI
    project_id UUID REFERENCES projects(id),
    error_message TEXT,
    credit_cost INTEGER,
    generation_duration_ms INTEGER,
    retry_count INTEGER DEFAULT 0,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT DEFAULT 'Untitled Project',
    prompt_id UUID REFERENCES prompts(id),
    tool_id TEXT NOT NULL REFERENCES tools(id),
    input_file_url TEXT,
    prompt_text TEXT,
    credit_cost INTEGER,
    generation_duration_ms INTEGER,
    server_type TEXT, -- 'slow', 'fast'
    outputs JSONB, -- Array of output URLs and metadata
    is_favorite BOOLEAN DEFAULT false,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    saved_at TIMESTAMPTZ DEFAULT NOW()
);