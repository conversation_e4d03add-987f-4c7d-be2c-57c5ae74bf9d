# 🗄️ **AiGenius Create Platform - Database Schema Implementation Plan**

## 📋 **Phase 1: Database Schema Fix - COMPLETED**

### **✅ Fixed Hasura Metadata File: `hasura_metadata_fixed.json`**

The new metadata file includes:

#### **🔐 Auth Tables (Nhost Standard)**
- `auth.users` - User authentication with proper relationships
- `auth.refresh_tokens` - Session management
- `auth.roles` - User roles and permissions
- `auth.user_providers` - OAuth providers
- `auth.user_roles` - User role assignments
- `auth.user_security_keys` - Security keys

#### **💰 Wallet & Credit System**
```sql
-- Plans table for subscription tiers
CREATE TABLE plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- 'Visitor Pass Free', 'Visitor Pass Paid', 'Citizen Pass Lite', etc.
    server_type_access TEXT NOT NULL, -- 'slow', 'fast', 'both'
    allows_overdraft BOOLEAN DEFAULT false,
    overdraft_limit INTEGER DEFAULT 0,
    price_monthly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    features JSON<PERSON>, -- Plan features as JSON
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User wallet with proper schema
CREATE TABLE user_wallet (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits INTEGER DEFAULT 0, -- General credits
    fast_credits_remaining INTEGER DEFAULT 0, -- Fast generation credits
    plan_id UUID REFERENCES plans(id), -- Current plan
    last_free_credit_grant TIMESTAMPTZ, -- Last time free credits were granted
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### **🎯 Generation System**
```sql
-- Prompts table with comprehensive schema
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tool_id TEXT NOT NULL, -- 'reimagine', 'img2video', etc.
    input_file TEXT, -- Original filename
    user_prompt TEXT, -- User's text prompt
    server_type TEXT NOT NULL, -- 'slow', 'fast'
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    project_id UUID REFERENCES projects(id), -- Link to saved project
    error_message TEXT, -- Error details if failed
    uploaded_file_id UUID REFERENCES storage.files(id), -- Nhost storage file ID
    output_file TEXT, -- Generated output filename
    credit_cost INTEGER, -- Credits consumed
    retry_count INTEGER DEFAULT 0,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Projects table for saved generations
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL, -- User-defined project name
    prompt_id UUID REFERENCES prompts(id), -- Link to original prompt
    tool_id TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    outputs JSONB, -- Generated outputs as JSON
    credit_cost INTEGER,
    generation_duration_ms INTEGER,
    server_used_type TEXT,
    output_url TEXT, -- Public URL to generated content
    output_type TEXT, -- 'image', 'video', etc.
    output_file_name TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    saved_at TIMESTAMPTZ DEFAULT NOW()
);

-- Credit transactions for audit trail
CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    prompt_id UUID REFERENCES prompts(id), -- Link to generation that caused transaction
    change_amount INTEGER NOT NULL, -- Positive for credits added, negative for deducted
    balance_after INTEGER NOT NULL, -- Balance after transaction
    fast_balance_after INTEGER, -- Fast credits balance after transaction
    deducted_from TEXT, -- 'credits', 'fast_credits', 'overdraft'
    reason TEXT NOT NULL, -- 'generation', 'purchase', 'free_daily', etc.
    notes TEXT, -- Additional details
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### **🛠️ Tools & Infrastructure**
```sql
-- Tools configuration
CREATE TABLE tools (
    id TEXT PRIMARY KEY, -- 'reimagine', 'img2video'
    name TEXT NOT NULL,
    description TEXT,
    workflow_file_id UUID REFERENCES storage.files(id), -- ComfyUI workflow in 'sauces' bucket
    is_active BOOLEAN DEFAULT true,
    cost_credits_per_second DECIMAL(10,4), -- Cost calculation
    input_types TEXT[], -- ['image/jpeg', 'image/png']
    output_types TEXT[], -- ['image/jpeg', 'video/mp4']
    category TEXT, -- 'image', 'video', 'audio'
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ComfyUI servers for load balancing
CREATE TABLE comfyui_servers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL, -- Server endpoint
    server_type TEXT NOT NULL, -- 'slow', 'fast'
    is_active BOOLEAN DEFAULT true,
    tool_id TEXT, -- Specific tool or 'all' for general purpose
    queue_priority INTEGER DEFAULT 0, -- Lower number = higher priority
    max_concurrent_jobs INTEGER DEFAULT 1,
    current_jobs INTEGER DEFAULT 0,
    last_health_check TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### **📁 Storage Integration**
- `storage.buckets` - Nhost storage buckets configuration
- `storage.files` - File metadata with proper user permissions
- **Buckets**: `user_inputs`, `user_outputs`, `sauces` (workflows)

### **🔑 Key Features of Fixed Schema:**

#### **1. Proper Permissions & Security**
- ✅ All tables have user-scoped permissions (`user_id = X-Hasura-User-Id`)
- ✅ Backend-only operations for sensitive data (credit transactions)
- ✅ Proper foreign key relationships and constraints

#### **2. Complete Audit Trail**
- ✅ All generation attempts logged in `prompts` table
- ✅ Credit transactions tracked with full context
- ✅ File uploads linked to storage system

#### **3. Flexible Plan System**
- ✅ Support for all 4 planned tiers (Visitor Free/Paid, Citizen Lite/Pro/Elite)
- ✅ Server type access control (slow/fast/both)
- ✅ Overdraft capabilities for premium plans

#### **4. Robust Generation Workflow**
- ✅ Status tracking from pending → processing → completed/failed
- ✅ Error message storage for debugging
- ✅ Project linking for saved generations
- ✅ File upload tracking with Nhost storage

---

## 📋 **Phase 2: Nhost Functions Development**

### **🔧 Functions to Create in `/functions` folder:**

#### **1. Daily Free Credits (`/functions/daily-free-credits.ts`)**
```typescript
// Grant 100 free credits daily based on available balance
// Check last_free_credit_grant timestamp
// Update user_wallet and create credit_transaction
```

#### **2. ComfyUI Load Balancer (`/functions/comfyui-load-balancer.ts`)**
```typescript
// Round-robin method with 30 jobs per instance limit
// Health check ComfyUI servers
// Return optimal server for generation request
```

#### **3. Credit Management (`/functions/credit-operations.ts`)**
```typescript
// Deduct credits for generations
// Handle overdraft logic
// Create transaction records
```

#### **4. Generation Status Updater (`/functions/generation-status.ts`)**
```typescript
// Update prompt status and completion data
// Handle success/failure scenarios
// Link to projects when saved
```

---

## 📋 **Phase 3: Frontend Code Updates**

### **🔧 Required Updates:**

#### **1. Fix GraphQL Queries**
- ✅ Update `user_wallet_by_pk` to use correct primary key
- ✅ Fix prompts table field names (`user_id`, `input_file`, etc.)
- ✅ Add proper error handling for new schema

#### **2. Update Service Files**
- ✅ `creditService.js` - Fix wallet queries
- ✅ `generationService.js` - Update prompt logging
- ✅ Add new functions for project management

#### **3. Component Updates**
- ✅ `Dashboard.jsx` - Display wallet info correctly
- ✅ `History.jsx` - Show new prompt fields
- ✅ `Projects.jsx` - Handle project linking

---

## 🚀 **Implementation Steps:**

### **Step 1: Import Fixed Metadata**
```bash
# In Hasura Console, go to Settings > Import Metadata
# Upload hasura_metadata_fixed.json
# This will fix all table permissions and relationships
```

### **Step 2: Verify Database Schema**
```sql
-- Check if tables exist with correct structure
\d+ user_wallet
\d+ prompts
\d+ projects
\d+ credit_transactions
```

### **Step 3: Test Frontend Integration**
```javascript
// Test wallet query
const { data } = await nhost.graphql.request(`
  query GetUserWallet($userId: uuid!) {
    user_wallet_by_pk(user_id: $userId) {
      user_id
      credits
      fast_credits_remaining
      plan { name }
    }
  }
`, { userId });
```

This comprehensive plan addresses all the current database issues and provides a robust foundation for the AiGenius Create platform.
