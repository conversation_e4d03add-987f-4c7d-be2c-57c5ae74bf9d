-- Wallet System (Financial)
CREATE TABLE user_wallet (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_balance INTEGER DEFAULT 0, -- Regular credits (for pay-as-you-go)
    fast_credits_balance INTEGER DEFAULT 0, -- Fast generation credits
    last_free_credit_grant TIMESTAMPTZ, -- For daily free credits
    active_pass_id UUID REFERENCES user_passes(id), -- Current active pass
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pass System (Access Rights)
CREATE TABLE pass_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- 'Visitor Free', 'Visitor Paid', 'Citizen Lite', 'Citizen Pro', 'Citizen Elite'
    tier TEXT NOT NULL, -- 'Free', 'Paid', 'Lite', 'Pro', 'Elite'
    category TEXT NOT NULL, -- 'Visitor', 'Citizen'
    monthly_price DECIMAL(10,2),
    hours_included INTEGER, -- NULL for unlimited
    is_unlimited BOOLEAN DEFAULT false,
    slow_generation_limit INTEGER, -- NULL for unlimited
    fast_generation_limit INTEGER, -- NULL for unlimited
    provider_price_id TEXT, -- For payment integration
    features JSONB, -- Additional features as JSON
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE user_passes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    pass_type_id UUID NOT NULL REFERENCES pass_types(id),
    start_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    end_date TIMESTAMPTZ, -- NULL for lifetime passes
    status TEXT NOT NULL DEFAULT 'active', -- 'active', 'expired', 'cancelled'
    hours_used INTEGER DEFAULT 0, -- Track usage for limited passes
    provider_subscription_id TEXT, -- For subscription management
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Credit Bundles (For Visitor Paid tier)
CREATE TABLE credit_bundles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL, -- '$1 - 30 credits', etc.
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    credits_amount INTEGER NOT NULL,
    is_featured BOOLEAN DEFAULT false,
    provider_price_id TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);