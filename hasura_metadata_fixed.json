{"resource_version": 130, "metadata": {"version": 3, "sources": [{"name": "default", "kind": "postgres", "tables": [{"table": {"name": "provider_requests", "schema": "auth"}, "configuration": {"column_config": {"id": {"custom_name": "id"}, "options": {"custom_name": "options"}}, "custom_column_names": {"id": "id", "options": "options"}, "custom_name": "authProviderRequests", "custom_root_fields": {"delete": "deleteAuthProviderRequests", "delete_by_pk": "deleteAuthProviderRequest", "insert": "insertAuthProviderRequests", "insert_one": "insertAuthProviderRequest", "select": "authProviderRequests", "select_aggregate": "authProviderRequestsAggregate", "select_by_pk": "authProviderRequest", "update": "updateAuthProviderRequests", "update_by_pk": "updateAuthProviderRequest"}}}, {"table": {"name": "providers", "schema": "auth"}, "configuration": {"column_config": {"id": {"custom_name": "id"}}, "custom_column_names": {"id": "id"}, "custom_name": "authProviders", "custom_root_fields": {"delete": "deleteAuthProviders", "delete_by_pk": "deleteAuthProvider", "insert": "insertAuthProviders", "insert_one": "insertAuthProvider", "select": "authProviders", "select_aggregate": "authProvidersAggregate", "select_by_pk": "authProvider", "update": "updateAuthProviders", "update_by_pk": "updateAuthProvider"}}, "array_relationships": [{"name": "userProviders", "using": {"foreign_key_constraint_on": {"column": "provider_id", "table": {"name": "user_providers", "schema": "auth"}}}}]}, {"table": {"name": "refresh_token_types", "schema": "auth"}, "is_enum": true, "configuration": {"column_config": {}, "custom_column_names": {}, "custom_name": "authRefreshTokenTypes", "custom_root_fields": {"delete": "deleteAuthRefreshTokenTypes", "delete_by_pk": "deleteAuthRefreshTokenType", "insert": "insertAuthRefreshTokenTypes", "insert_one": "insertAuthRefreshTokenType", "select": "authRefreshTokenTypes", "select_aggregate": "authRefreshTokenTypesAggregate", "select_by_pk": "authRefreshTokenType", "update": "updateAuthRefreshTokenTypes", "update_by_pk": "updateAuthRefreshTokenType"}}, "array_relationships": [{"name": "refreshTokens", "using": {"foreign_key_constraint_on": {"column": "type", "table": {"name": "refresh_tokens", "schema": "auth"}}}}]}, {"table": {"name": "refresh_tokens", "schema": "auth"}, "configuration": {"column_config": {"created_at": {"custom_name": "createdAt"}, "expires_at": {"custom_name": "expiresAt"}, "refresh_token_hash": {"custom_name": "refreshTokenHash"}, "user_id": {"custom_name": "userId"}}, "custom_column_names": {"created_at": "createdAt", "expires_at": "expiresAt", "refresh_token_hash": "refreshTokenHash", "user_id": "userId"}, "custom_name": "authRefreshTokens", "custom_root_fields": {"delete": "deleteAuthRefreshTokens", "delete_by_pk": "deleteAuthRefreshToken", "insert": "insertAuthRefreshTokens", "insert_one": "insertAuthRefreshToken", "select": "authRefreshTokens", "select_aggregate": "authRefreshTokensAggregate", "select_by_pk": "authRefreshToken", "update": "updateAuthRefreshTokens", "update_by_pk": "updateAuthRefreshToken"}}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "created_at", "expires_at", "metadata", "type", "user_id"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"_and": [{"user_id": {"_eq": "X-Hasura-User-Id"}}, {"type": {"_eq": "pat"}}]}}}]}, {"table": {"name": "roles", "schema": "auth"}, "configuration": {"custom_name": "authRoles"}}, {"table": {"name": "user_providers", "schema": "auth"}, "configuration": {"custom_name": "authUserProviders"}}, {"table": {"name": "user_roles", "schema": "auth"}, "configuration": {"custom_name": "authUserRoles"}}, {"table": {"name": "user_security_keys", "schema": "auth"}, "configuration": {"custom_name": "authUserSecurityKeys"}}, {"table": {"name": "users", "schema": "auth"}, "configuration": {"custom_name": "users", "custom_root_fields": {"select_by_pk": "user"}}, "object_relationships": [{"name": "user_wallet", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "user_wallet", "schema": "public"}}}}], "array_relationships": [{"name": "prompts", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "prompts", "schema": "public"}}}}, {"name": "projects", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "projects", "schema": "public"}}}}, {"name": "credit_transactions", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "credit_transactions", "schema": "public"}}}}]}, {"table": {"name": "plans", "schema": "public"}, "array_relationships": [{"name": "user_wallets", "using": {"foreign_key_constraint_on": {"column": "plan_id", "table": {"name": "user_wallet", "schema": "public"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "name", "server_type_access", "allows_overdraft", "overdraft_limit", "price_monthly", "price_yearly", "features", "is_active"], "filter": {}}}]}, {"table": {"name": "user_wallet", "schema": "public"}, "object_relationships": [{"name": "plan", "using": {"foreign_key_constraint_on": "plan_id"}}, {"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}], "array_relationships": [{"name": "credit_transactions", "using": {"foreign_key_constraint_on": {"column": "user_id", "table": {"name": "credit_transactions", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "set": {"user_id": "X-Hasura-User-Id"}, "columns": ["credits", "fast_credits_remaining", "plan_id", "last_free_credit_grant"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["user_id", "credits", "fast_credits_remaining", "plan_id", "last_free_credit_grant", "created_at", "updated_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["credits", "fast_credits_remaining", "plan_id", "last_free_credit_grant"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "check": null}}]}, {"table": {"name": "prompts", "schema": "public"}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}, {"name": "project", "using": {"foreign_key_constraint_on": "project_id"}}, {"name": "uploaded_file", "using": {"foreign_key_constraint_on": "uploaded_file_id"}}], "array_relationships": [{"name": "credit_transactions", "using": {"foreign_key_constraint_on": {"column": "prompt_id", "table": {"name": "credit_transactions", "schema": "public"}}}}], "insert_permissions": [{"role": "user", "permission": {"check": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "set": {"user_id": "X-Hasura-User-Id"}, "columns": ["tool_id", "input_file", "user_prompt", "server_type", "status", "project_id", "error_message", "uploaded_file_id"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "user_id", "tool_id", "input_file", "user_prompt", "server_type", "status", "project_id", "error_message", "uploaded_file_id", "output_file", "credit_cost", "retry_count", "completed_at", "created_at", "updated_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["status", "project_id", "error_message", "output_file", "credit_cost", "retry_count", "completed_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "check": null}}]}, {"table": {"name": "projects", "schema": "public"}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}, {"name": "prompt", "using": {"foreign_key_constraint_on": "prompt_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "set": {"user_id": "X-Hasura-User-Id"}, "columns": ["name", "prompt_id", "tool_id", "status", "outputs", "credit_cost", "generation_duration_ms", "server_used_type", "output_url", "output_type", "output_file_name"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "user_id", "name", "prompt_id", "tool_id", "status", "outputs", "credit_cost", "generation_duration_ms", "server_used_type", "output_url", "output_type", "output_file_name", "created_at", "updated_at", "saved_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["name", "status", "outputs", "output_url", "output_type", "output_file_name", "saved_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}, "check": null}}]}, {"table": {"name": "credit_transactions", "schema": "public"}, "object_relationships": [{"name": "user", "using": {"foreign_key_constraint_on": "user_id"}}, {"name": "prompt", "using": {"foreign_key_constraint_on": "prompt_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {}, "columns": [], "backend_only": true}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "user_id", "prompt_id", "change_amount", "balance_after", "fast_balance_after", "deducted_from", "reason", "notes", "created_at"], "filter": {"user_id": {"_eq": "X-Hasura-User-Id"}}}}]}, {"table": {"name": "tools", "schema": "public"}, "select_permissions": [{"role": "user", "permission": {"columns": ["id", "name", "description", "workflow_file_id", "is_active", "cost_credits_per_second", "input_types", "output_types", "category"], "filter": {"is_active": {"_eq": true}}}}]}, {"table": {"name": "comfyui_servers", "schema": "public"}, "select_permissions": [{"role": "user", "permission": {"columns": ["id", "url", "server_type", "is_active", "tool_id", "queue_priority"], "filter": {"is_active": {"_eq": true}}}}]}, {"table": {"name": "buckets", "schema": "storage"}, "configuration": {"custom_name": "buckets"}, "array_relationships": [{"name": "files", "using": {"foreign_key_constraint_on": {"column": "bucket_id", "table": {"name": "files", "schema": "storage"}}}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "created_at", "updated_at", "presigned_urls_enabled", "download_expiration", "max_upload_file_size", "min_upload_file_size", "cache_control"], "filter": {}}}]}, {"table": {"name": "files", "schema": "storage"}, "configuration": {"custom_name": "files"}, "object_relationships": [{"name": "bucket", "using": {"foreign_key_constraint_on": "bucket_id"}}], "insert_permissions": [{"role": "user", "permission": {"check": {"uploaded_by_user_id": {"_eq": "X-Hasura-User-Id"}}, "set": {"uploaded_by_user_id": "X-Hasura-User-Id"}, "columns": ["id", "name", "size", "bucket_id", "etag", "mime_type", "metadata", "is_uploaded", "created_at", "updated_at"]}}], "select_permissions": [{"role": "user", "permission": {"columns": ["id", "name", "size", "bucket_id", "etag", "mime_type", "metadata", "is_uploaded", "uploaded_by_user_id", "created_at", "updated_at"], "filter": {"uploaded_by_user_id": {"_eq": "X-Hasura-User-Id"}}}}], "update_permissions": [{"role": "user", "permission": {"columns": ["name", "size", "bucket_id", "etag", "mime_type", "metadata", "is_uploaded", "updated_at"], "filter": {"uploaded_by_user_id": {"_eq": "X-Hasura-User-Id"}}, "check": null}}], "delete_permissions": [{"role": "user", "permission": {"filter": {"uploaded_by_user_id": {"_eq": "X-Hasura-User-Id"}}}}]}], "configuration": {"connection_info": {"database_url": {"from_env": "HASURA_GRAPHQL_DATABASE_URL"}, "isolation_level": "read-committed", "pool_settings": {"connection_lifetime": 600, "idle_timeout": 180, "max_connections": 50, "retries": 1}, "use_prepared_statements": true}}}]}}