// src/pages/Img2Video.jsx
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
    Container, Typography, TextField, Button, Box, CircularProgress, Grid,
    Paper, Avatar, Snackbar, Alert as MuiAlert, Fade, Card, CardMedia, CardContent
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
// Import Nhost hooks
import { useAuthenticationStatus, useUserId } from '@nhost/react';
// Removed Firebase imports
import { StorageSystem } from '../services/storageSystem'; // Use the updated StorageSystem
import UploadFileIcon from '@mui/icons-material/UploadFile';
import AIGenerationLoader from '../components/AIGenerationLoader';
// Removed AnimatedTextField import, assuming standard TextField for now based on previous code
import AnimatedTextField from '../components/AnimatedTextField';

// Use Alert inside Snackbar for styling
const Alert = React.forwardRef(function Alert(props, ref) {
  return <MuiAlert elevation={6} ref={ref} variant="filled" {...props} />;
});

// --- !!! IMPORTANT: Replace with your actual Nhost File ID !!! ---
// You get this ID after uploading 'Videogen1.json' to Nhost Storage.
const WORKFLOW_FILE_ID_IMG2VIDEO = 'f5c557a0-8af8-4062-880e-26bbc3ce79b3'; // Replace this placeholder

function Img2Video() {
    const theme = useTheme();
    // --- Nhost Hooks ---
    const { isLoading: isLoadingAuth, isAuthenticated } = useAuthenticationStatus();
    const userId = useUserId(); // Get current user ID

    // --- State --- (Remains mostly the same)
    const [file, setFile] = useState(null);
    const [previewUrl, setPreviewUrl] = useState(null);
    const [prompt, setPrompt] = useState(''); // Prompt for motion/style
    const [outputUrl, setOutputUrl] = useState('');
    const [loading, setLoading] = useState(false); // For the generation process
    const [outputObjectForSave, setOutputObjectForSave] = useState(null);
    const isGenerating = useRef(false);
    const [saveInProgress, setSaveInProgress] = useState(false);
    const [error, setError] = useState(null); // For displaying generation errors
    const [toast, setToast] = useState({ open: false, message: '', severity: 'info' });

    // --- Config ---
    const toolId = 'img2video';
    // Node IDs from Videogen1.json (verify these match your actual workflow)
    const fileNodeId = '96'; // Node ID for the input image
    const promptNodeId = '6'; // Node ID for the text prompt
    const promptInputKey = 'text'; // Input key within the prompt node

    // --- Toast Handlers --- (Keep as before)
    const handleCloseToast = (event, reason) => {
      if (reason === 'clickaway') { return; }
      setToast(prev => ({ ...prev, open: false }));
    };
    const showToast = useCallback((message, severity = 'info') => {
        setToast({ open: true, message, severity });
    }, []);

    // --- Preview Effect --- (Keep as before)
    useEffect(() => {
        let o=null; if(file)o=URL.createObjectURL(file); setPreviewUrl(o); return ()=>{if(o) URL.revokeObjectURL(o);};
    }, [file]);

    // --- File Change Handler --- (Keep as before)
    const handleFileChange = (e) => {
        setOutputUrl('');
        setOutputObjectForSave(null);
        setError(null);
        showToast('Ready for new generation.', 'info');
        setFile(e.target.files?.[0] || null);
    };

    // === handleSubmit using updated StorageSystem ===
    const handleSubmit = async (e) => {
        e.preventDefault();
        // Use Nhost's isAuthenticated and userId
        if (!isAuthenticated || !userId || !file || isGenerating.current) {
            showToast(isGenerating.current ? "Generation already in progress." : "User, file, or authentication missing.", 'warning');
            return;
        }
        // Check if placeholder File ID is replaced
        if (WORKFLOW_FILE_ID_IMG2VIDEO === 'YOUR_NHOST_STORAGE_FILE_ID_FOR_VIDEOGEN1_JSON') {
             showToast("Workflow File ID is not configured. Please update the code.", 'error');
             console.error("WORKFLOW_FILE_ID_IMG2VIDEO placeholder not replaced in src/pages/Img2Video.jsx");
             return;
        }

        setLoading(true); showToast('Initiating video generation...', 'info');
        setOutputUrl(''); setOutputObjectForSave(null); isGenerating.current = true; setError(null);
        let comfyInstanceUrl = null;

        try {
            // Calls to StorageSystem remain the same
            showToast('Locating AI server...');
            comfyInstanceUrl = await StorageSystem.getComfyInstanceUrl(toolId); // Uses GraphQL

            showToast('Loading AI workflow...');
            const workflowTemplate = await StorageSystem.getWorkflowTemplate(WORKFLOW_FILE_ID_IMG2VIDEO); // Uses Nhost Storage

            showToast('Uploading image...');
            const comfyInputFileName = await StorageSystem.uploadInputToComfy(file, comfyInstanceUrl); // Direct to Comfy

            showToast('Preparing AI job...');
            const workflowPayload = JSON.parse(JSON.stringify(workflowTemplate));
            // Verify nodes exist based on config
            if (!workflowPayload?.[fileNodeId]?.inputs) throw new Error(`Template Error: Node ${fileNodeId} Missing`);
            if (!workflowPayload?.[promptNodeId]?.inputs?.[promptInputKey]) throw new Error(`Template Error: Node/Key ${promptNodeId}.${promptInputKey} Missing`);

            workflowPayload[fileNodeId].inputs.image = comfyInputFileName;
            workflowPayload[promptNodeId].inputs[promptInputKey] = prompt.trim() || 'Animate this image'; // Video default prompt

            // Log attempt using Nhost user ID and workflow File ID
            StorageSystem.logPromptAttempt(userId, toolId, file.name, prompt.trim() || 'Animate this image', WORKFLOW_FILE_ID_IMG2VIDEO).catch(e=>console.warn("Log fail:",e)); // Uses GraphQL

            showToast('Sending job to AI...');
            const submittedPromptId = await StorageSystem.submitWorkflowToComfy(workflowPayload, comfyInstanceUrl); // Direct to Comfy

            showToast('AI processing... waiting for video results...', 'info');
            // Polls Comfy, triggers worker (uses Nhost JWT), worker saves to R2
            const outputResult = await StorageSystem.pollComfyOutputAndSave(submittedPromptId, toolId, comfyInstanceUrl);

            setOutputUrl(outputResult.r2PublicUrl);
            const finalOutputData = {
                 promptId: submittedPromptId,
                 outputUrl: outputResult.r2PublicUrl,
                 outputR2Key: outputResult.r2OutputKey,
                 toolId: toolId,
                 userId: userId, // Use Nhost userId
                 timestamp: new Date().toISOString(),
                 originalFileName: file.name,
                 promptUsed: prompt.trim() || 'Animate this image',
                 contentType: outputResult.contentType, // Should be video/*
                 comfyOutputFileName: outputResult.comfyOutputFileName,
                 outputType: 'video', // Mark as video
             };
            setOutputObjectForSave(finalOutputData);
            showToast('Output Video Ready!', 'success');
            localStorage.setItem(`output-${submittedPromptId}`, JSON.stringify(finalOutputData));

        } catch (err) {
            console.error('Generation Failed (Img2Video.jsx):', err);
            showToast(err.message || "An error occurred during video generation.", 'error');
            setError(err.message || "An error occurred during video generation.");
        } finally {
            setLoading(false); isGenerating.current = false;
        }
    };

    // === handleSaveProject using updated StorageSystem ===
    const handleSaveProject = async () => {
        if (!outputObjectForSave) { showToast('Nothing to save.', 'warning'); return; }
        setSaveInProgress(true); showToast('Saving project...', 'info');
        try {
            // Calls StorageSystem which now uses GraphQL mutation
            await StorageSystem.saveProjectMetadata(outputObjectForSave);
            showToast('Project saved!', 'success');
            setOutputObjectForSave(null); // Disable button
            if (outputObjectForSave.promptId) {
                localStorage.removeItem(`output-${outputObjectForSave.promptId}`);
            }
        } catch (err) {
             console.error('Save metadata failed:', err);
             showToast(`Save failed: ${err.message}`, 'error');
        }
        finally { setSaveInProgress(false); }
    };

    // --- UI Rendering ---
    if (isLoadingAuth) {
        return (<Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}><CircularProgress /></Box>);
    }

    return (
        <Container
            maxWidth={false}
            sx={{
                py: { xs: 2, sm: 3, md: 4 },
                px: { xs: 2, sm: 3, md: 4 },
                backgroundColor: theme.palette.background.default,
            }}
        >
             <Box sx={{ mb: 4 }}>
                <Typography variant="h1" component="h1">Image to Video Tool</Typography>
                <Typography variant="body1" color="text.secondary">Upload image, optional motion prompt, output video securely.</Typography>
             </Box>

            <Snackbar open={toast.open} autoHideDuration={4000} onClose={handleCloseToast} anchorOrigin={{ vertical: 'top', horizontal: 'right' }} TransitionComponent={Fade} sx={{ mt: '64px' }}>
                <Alert onClose={handleCloseToast} severity={toast.severity} sx={{ width: '100%', borderRadius: theme.shape.borderRadius }}>{toast.message}</Alert>
            </Snackbar>

            <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: theme.spacing(3) }}>
                {/* Column 1: Input Settings */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <Typography variant="h2" component="h3" sx={{ mb: 2 }}>Input Settings</Typography>
                    <Paper component="form" onSubmit={handleSubmit} elevation={0} sx={{ p: theme.spacing(3), borderRadius: '16px', border: `1px solid ${theme.palette.divider}`, bgcolor: 'background.paper', flexGrow: 1, display: 'flex', flexDirection: 'column', gap: theme.spacing(2.5) }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
                            <Button variant="outlined" component="label" startIcon={<UploadFileIcon />} disabled={!isAuthenticated || loading} fullWidth={!previewUrl} sx={{ flexShrink: 0 }}>Choose Image<input type="file" onChange={handleFileChange} hidden accept="image/png, image/jpeg, image/webp" required /></Button>
                            {previewUrl && <Avatar variant="rounded" src={previewUrl} alt="Preview" sx={{ width: 70, height: 70, border: `1px solid ${theme.palette.divider}`, borderRadius: theme.shape.borderRadius }} />}
                        </Box>
                        {file && <Typography variant="caption" display="block" sx={{ wordBreak: 'break-all', color: 'text.secondary' }}>{file.name}</Typography>}

                        {/* Standard TextField for prompt */}
                        <AnimatedTextField
                            label="Motion Prompt (Optional)"
                            multiline
                            rows={4}
                            fullWidth
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            disabled={!isAuthenticated || loading}
                            placeholder="Describe motion, style, e.g., 'gentle zoom in', 'camera pans right'"
                        />
                        <Box>
                            <Button type="submit" variant="contained" color="primary" disabled={!isAuthenticated || !file || loading}>
                                {loading ? 'Processing...' : 'Generate Video'}
                            </Button>
                        </Box>
                    </Paper>
                </Box>

                {/* Column 2: Output Area */}
                <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                     <Typography variant="h2" component="h3" sx={{ mb: 2 }}> {loading ? 'Generating Video...' : outputUrl ? 'Output Video' : 'Output Area'} </Typography>
                     <Paper elevation={0} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '300px', flexGrow: 1, borderRadius: '16px', border: `1px solid ${theme.palette.divider}`, bgcolor: 'background.paper', p: loading ? 0 : 2, overflow: 'hidden', boxSizing: 'border-box' }}>
                        {loading ? (
                           <Box sx={{width: '100%', height: '100%', display:'flex', alignItems:'center', justifyContent:'center'}}><AIGenerationLoader /></Box>
                        ) : outputUrl ? (
                           <Card elevation={0} sx={{width: '100%', height:'100%', boxShadow:'none', border:'none', background:'transparent', display:'flex', flexDirection:'column'}}>
                                <Box sx={{ flexGrow: 1, display: 'flex', alignItems:'center', justifyContent:'center', width: '100%', bgcolor: '#000' /* Dark BG for video */ }}>
                                    <CardMedia
                                        component="video" // Use video component
                                        src={outputUrl}
                                        controls // Add video controls
                                        sx={{ display: 'block', width: 'auto', height: 'auto', maxHeight: '100%', maxWidth: '100%', objectFit: 'contain' }}
                                    />
                                </Box>
                                <CardContent sx={{ textAlign: 'center', pt: 2, pb:0 }}>
                                    <Button variant="contained" color="secondary" onClick={handleSaveProject} disabled={!outputObjectForSave || saveInProgress}> {saveInProgress? 'Saving...' : 'Save to My Projects'} </Button>
                                </CardContent>
                           </Card>
                        ) : error ? (
                             <Typography color="error" sx={{textAlign:'center'}}>Video Generation Failed</Typography>
                        ) : (
                             <Typography color="text.secondary" sx={{textAlign:'center'}}>Output video will appear here</Typography>
                        )}
                    </Paper>
                </Box>
            </Box>
        </Container>
    );
}
export default Img2Video;
