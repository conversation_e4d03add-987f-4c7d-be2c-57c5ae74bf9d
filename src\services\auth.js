// src/services/auth.js
import { nhost, safeRefreshToken } from './nhost'; // Import the initialized Nhost client and refresh function
import { gql } from '@apollo/client'; // Import gql for GraphQL queries
import { mapNhostError } from '../utils/validation'; // Import error mapping utility

/**
 * Logs in a user using Nhost authentication.
 * Handles the specific case where the user exists but their email is not verified.
 *
 * @param {string} email - User's email.
 * @param {string} password - User's password.
 * @returns {Promise<{ session: object|null, needsVerification?: boolean, error?: object }>}
 *          - If successful and verified: { session: NhostSessionObject }
 *          - If successful but needs verification: { session: null, needsVerification: true }
 *          - If failed: { session: null, error: ErrorObject }
 */
export const login = async (email, password) => {
  console.log(`[AuthService] Attempting Nhost login for: ${email}`);

  // Validate inputs
  if (!email || !password) {
    throw new Error('Email and password are required');
  }

  try {
    const { session, error, user } = await nhost.auth.signIn({
      email: email.trim(),
      password: password,
    });

    // --- Handle Nhost Errors ---
    if (error) {
      console.warn('[AuthService] Nhost signIn returned an error:', error);

      // ** CRITICAL: Check for unverified user error **
      // Nhost error object structure might vary slightly, check common patterns.
      if (error.error === 'unverified-user' ||
          error.message?.includes('Email needs verification') ||
          error.message?.includes('unverified') ||
          error.message?.includes('not verified')) {
        console.log('[AuthService] Login detected unverified user. Returning needsVerification flag.');
        // Store email for verification page
        sessionStorage.setItem('pendingVerificationEmail', email.trim());
        return { session: null, needsVerification: true, email: email.trim() };
      } else {
        // Map error to user-friendly message and throw
        const friendlyMessage = mapNhostError(error);
        console.error('[AuthService] Login failed with error:', error.message);
        throw new Error(friendlyMessage);
      }
    }

    // --- Handle Success ---
    // If no error, login was successful and user is verified (or verification not required by backend settings)
    console.log('[AuthService] Nhost login successful');

    // Clear any pending verification email
    sessionStorage.removeItem('pendingVerificationEmail');

    // Check if user is verified (additional safety check)
    if (user && user.emailVerified === false) {
      console.log('[AuthService] User logged in but email not verified, redirecting to verification');
      sessionStorage.setItem('pendingVerificationEmail', email.trim());
      return { session: null, needsVerification: true, email: email.trim() };
    }

    return { session, user }; // Return the session and user objects

  } catch (err) {
    // Catch unexpected issues during the process
    console.error('[AuthService] Unexpected error during login process:', err);

    // If it's already a mapped error, re-throw it
    if (err.message && err.message !== 'An unexpected error occurred during login.') {
      throw err;
    }

    // Otherwise, throw a generic error
    throw new Error('An unexpected error occurred during login. Please try again.');
  }
};

/**
 * Registers a new user using Nhost authentication.
 * Nhost automatically creates the user in the database on successful signup
 * and typically sends a verification email if configured.
 *
 * @param {string} email - User's email.
 * @param {string} password - User's password (ensure frontend checks length).
 * @param {string} username - User's chosen display name.
 * @returns {Promise<{ success: boolean, error?: object }>} Result object with success flag and optional error.
 */
export const register = async (email, password, username) => {
  console.log(`[AuthService] Attempting Nhost registration for: ${email}, Username: ${username}`);

  // Validate inputs
  if (!email || !password || !username) {
    throw new Error('Email, password, and username are required');
  }

  try {
    // Ensure username is not empty or just whitespace
    const trimmedUsername = username?.trim();
    const trimmedEmail = email?.trim();

    if (!trimmedUsername) {
      throw new Error('Username cannot be empty');
    }

    if (!trimmedEmail) {
      throw new Error('Email cannot be empty');
    }

    const { session, error, user } = await nhost.auth.signUp({
      email: trimmedEmail,
      password: password,
      options: {
        // Default role for new users (must exist in Hasura roles)
        defaultRole: 'user',
        // Allowed roles for this user
        allowedRoles: ['user'],
        // Store username in user metadata (accessible via Hasura/GraphQL)
        displayName: trimmedUsername,
        // Nhost automatically sends verification email if required by backend settings.
        // You can add locale if needed: locale: 'en'
        // You can add other metadata fields here if needed:
        // metadata: { custom_field: 'value' }
      },
    });

    if (error) {
      console.error('[AuthService] Nhost registration error:', error);
      // Map error to user-friendly message and throw
      const friendlyMessage = mapNhostError(error);
      throw new Error(friendlyMessage);
    }

    // Store email for verification page
    sessionStorage.setItem('pendingVerificationEmail', trimmedEmail);

    // Session might be null immediately after signup if verification is required.
    console.log('[AuthService] Nhost registration successful (session might be null until verification)');

    return {
      success: true,
      session,
      user,
      email: trimmedEmail,
      needsVerification: !user?.emailVerified
    };

  } catch (err) {
    console.error('[AuthService] Unexpected error during registration process:', err);

    // If it's already a mapped error, re-throw it
    if (err.message && !err.message.includes('An unexpected error occurred')) {
      throw err;
    }

    // Otherwise, throw a generic error
    throw new Error('An unexpected error occurred during registration. Please try again.');
  }
};

/**
 * Logs out the current user using Nhost.
 *
 * @returns {Promise<{ success: boolean, error?: object }>} Result object with success flag and optional error.
 */
export const logout = async () => {
  console.log('[AuthService] Attempting Nhost logout');
  try {
    const { error } = await nhost.auth.signOut();
    if (error) {
      console.error('[AuthService] Nhost logout error:', error);
      return { success: false, error };
    }
    console.log('[AuthService] Nhost logout successful');
    return { success: true };
  } catch (err) {
    console.error('[AuthService] Unexpected error during logout process:', err);
    return {
      success: false,
      error: {
        message: err instanceof Error ? err.message : 'An unexpected error occurred during logout.',
        originalError: err
      }
    };
  }
};

// --- GraphQL Query Definition ---
// Define the GraphQL query to fetch user data (credits, username)
// Ensure your 'users' table in Hasura has 'id', 'displayName', 'email', 'credits' columns
// and appropriate permissions are set for the 'user' role.
const GET_USER_DATA_QUERY = gql`
  query GetUserData($userId: uuid!) {
    # Use the actual table name from your Hasura schema (e.g., users, user_details)
    # Make sure the 'user' role has select permissions on these fields.
    users_by_pk(id: $userId) {
      id
      email
      displayName # Nhost maps this to the 'displayName' provided during signup
      # Add other custom fields you need from the 'users' table
      # credits
      # avatarUrl
      # plan_type
      # last_free_credit_grant
    }
  }
`;

/**
 * Retrieves user data (including custom fields like credits) from the Nhost backend using GraphQL.
 * Assumes the user is already authenticated, as Nhost includes the auth token automatically.
 *
 * @param {string} userId - The Nhost User ID (usually from `nhost.auth.getUser()?.id`).
 * @returns {Promise<{ success: boolean, userData?: object, error?: object }>} Result with user data or error.
 */
export const getUserData = async (userId) => {
  if (!userId) {
    console.warn('[AuthService] getUserData called without userId');
    return { success: false, error: { message: 'User ID is required' } };
  }

  console.log(`[AuthService] Fetching user data via GraphQL for userId: ${userId}`);

  try {
    // Check if we have a valid session first
    const session = nhost.auth.getSession();
    if (!session?.accessToken) {
      console.warn('[AuthService] No valid session found, attempting to refresh token');
      const refreshResult = await safeRefreshToken();
      if (!refreshResult.session) {
        return {
          success: false,
          error: { message: 'Authentication required', originalError: refreshResult.error }
        };
      }
    }

    // Use nhost.graphql.request for simple, authenticated queries
    const { data, error } = await nhost.graphql.request(
      GET_USER_DATA_QUERY,
      { userId }
      // Nhost automatically includes the Authorization header if the user is signed in
    );

    if (error) {
      console.error('[AuthService] GraphQL request error fetching user data:', error);
      return { success: false, error };
    }

    // Adjust the path based on your actual query structure
    const userData = data?.users_by_pk;

    if (!userData) {
      console.warn(`[AuthService] User data not found for userId: ${userId}`);
      return { success: false, error: { message: 'User data not found' } };
    }

    console.log('[AuthService] User data fetched successfully');
    // Map displayName to username if your app uses 'username' consistently elsewhere
    return {
      success: true,
      userData: { ...userData, username: userData.displayName }
    };

  } catch (err) {
    // Catch unexpected errors during the GraphQL request itself
    console.error('[AuthService] Unexpected error fetching user data via GraphQL:', err);
    return {
      success: false,
      error: {
        message: err instanceof Error ? err.message : 'Failed to fetch user data',
        originalError: err
      }
    };
  }
};

/**
 * Set up authentication event listeners and error handling
 */
export function setupAuthErrorHandling() {
  // Track authentication state to prevent duplicate handling
  let isHandlingAuthChange = false;
  let lastAuthEvent = null;
  let lastTokenChange = Date.now();

  // Listen for token changes
  nhost.auth.onTokenChanged((session) => {
    const now = Date.now();
    // Prevent handling token changes too frequently (debounce)
    if (now - lastTokenChange < 1000) {
      console.debug('[AuthService] Debouncing rapid token changes');
      return;
    }

    lastTokenChange = now;

    if (!session) {
      console.log('[AuthService] Token changed: No valid session');

      // Only attempt refresh if not already handling auth change
      if (!isHandlingAuthChange) {
        console.debug('[AuthService] Attempting to refresh token after token change to null');
        // Use setTimeout to avoid immediate refresh attempts during page transitions
        setTimeout(() => {
          // Only refresh if we're still not authenticated
          if (!isAuthenticated()) {
            refreshAuthToken(true).catch(err => {
              console.error('[AuthService] Error refreshing token after change:', err);
            });
          }
        }, 1000);
      }
    } else {
      console.log('[AuthService] Token changed: Valid session available');

      // Check token expiration
      if (session.accessTokenExpiresIn) {
        const expiresIn = new Date(session.accessTokenExpiresIn);
        console.debug(`[AuthService] Access token expires at: ${expiresIn.toLocaleTimeString()}`);
      }
    }
  });

  // Listen for authentication state changes
  nhost.auth.onAuthStateChanged((event) => {
    // Prevent duplicate handling of the same event
    if (event === lastAuthEvent && isHandlingAuthChange) {
      console.debug(`[AuthService] Ignoring duplicate auth event: ${event}`);
      return;
    }

    lastAuthEvent = event;
    isHandlingAuthChange = true;

    console.log(`[AuthService] Auth state changed: ${event}`);

    try {
      if (event === 'SIGNED_OUT') {
        // Clear any application state that depends on authentication
        console.log('[AuthService] User signed out, clearing application state');

        // Clear any cached data or state
        localStorage.removeItem('nhostStateChunks');

        // Ensure all auth-related localStorage items are removed
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('nhost') || key.includes('auth'))) {
            keysToRemove.push(key);
          }
        }

        keysToRemove.forEach(key => {
          console.debug(`[AuthService] Removing storage key: ${key}`);
          localStorage.removeItem(key);
        });

      } else if (event === 'SIGNED_IN') {
        // Initialize application state that depends on authentication
        console.log('[AuthService] User signed in, initializing application state');

        // Ensure we have the latest user data
        const user = nhost.auth.getUser();
        if (user) {
          console.debug(`[AuthService] User signed in: ${user.email}`);
        }
      }
    } finally {
      // Reset handling flag after a short delay
      setTimeout(() => {
        isHandlingAuthChange = false;
      }, 1000);
    }
  });

  // Set up error handling for network/API errors
  window.addEventListener('online', () => {
    console.log('[AuthService] Network connection restored, checking authentication');
    // When coming back online, check if we need to refresh the token
    if (isAuthenticated()) {
      console.debug('[AuthService] Still authenticated after coming online');
    } else {
      console.debug('[AuthService] Not authenticated after coming online, attempting refresh');
      refreshAuthToken(true).catch(err => {
        console.error('[AuthService] Error refreshing token after coming online:', err);
      });
    }
  });
}

/**
 * Manually refresh the authentication token with improved error handling
 * @param {boolean} force - Force refresh even if token is still valid
 * @returns {Promise<{ success: boolean, session?: object, error?: object }>} Result with refreshed session or error
 */
export async function refreshAuthToken(force = false) {
  console.log('[AuthService] Manually refreshing auth token');

  try {
    // Check if we already have a valid session before attempting refresh
    if (!force) {
      const currentSession = nhost.auth.getSession();
      if (currentSession?.accessToken) {
        // Check if token is about to expire (within 5 minutes)
        const expiresIn = currentSession.accessTokenExpiresIn;
        const now = Date.now();

        if (expiresIn && expiresIn > now + 5 * 60 * 1000) {
          console.log('[AuthService] Current token is still valid, skipping refresh');
          return { success: true, session: currentSession };
        }
      }
    }

    // Attempt to refresh the token
    const result = await safeRefreshToken(0, force);

    if (result.error) {
      console.error('[AuthService] Failed to refresh token:', result.error);

      // For specific error types, we might want to handle differently
      if (result.error.status === 401) {
        console.log('[AuthService] Unauthorized error during token refresh, user needs to login again');
        // Clear any application state that depends on authentication
        localStorage.removeItem('nhostStateChunks');
      }

      return { success: false, error: result.error };
    }

    console.log('[AuthService] Token refreshed successfully');
    return { success: true, session: result.session };
  } catch (error) {
    console.error('[AuthService] Error in refreshAuthToken:', error);
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'Failed to refresh token',
        originalError: error
      }
    };
  }
}

/**
 * Check if the user is authenticated with a valid session
 * @returns {boolean} True if the user has a valid session
 */
export function isAuthenticated() {
  const session = nhost.auth.getSession();
  if (!session) return false;

  // Check if access token exists and is not expired
  if (!session.accessToken) return false;

  // If we have expiry information, check if token is expired
  if (session.accessTokenExpiresIn) {
    const now = Date.now();
    if (session.accessTokenExpiresIn <= now) {
      console.debug('[AuthService] Access token is expired');
      return false;
    }
  }

  return true;
}

/**
 * Get the current user if authenticated
 * @returns {object|null} User object or null if not authenticated
 */
export function getCurrentUser() {
  if (!isAuthenticated()) return null;
  return nhost.auth.getUser();
}

/**
 * Get the current session if authenticated
 * @returns {object|null} Session object or null if not authenticated
 */
export function getCurrentSession() {
  if (!isAuthenticated()) return null;
  return nhost.auth.getSession();
}

/**
 * Check if user's email is verified
 * @returns {boolean} True if email is verified
 */
export function isEmailVerified() {
  const user = getCurrentUser();
  return user?.emailVerified === true;
}

/**
 * Get pending verification email from session storage
 * @returns {string|null} Email address or null
 */
export function getPendingVerificationEmail() {
  return sessionStorage.getItem('pendingVerificationEmail');
}

/**
 * Clear pending verification email from session storage
 */
export function clearPendingVerificationEmail() {
  sessionStorage.removeItem('pendingVerificationEmail');
}

/**
 * Send verification email
 * @param {string} email - Email address to send verification to
 * @returns {Promise<{ success: boolean, error?: object }>} Result object
 */
export async function sendVerificationEmail(email) {
  try {
    const { error } = await nhost.auth.sendVerificationEmail({
      email: email || getPendingVerificationEmail(),
    });

    if (error) {
      console.error('[AuthService] Send verification email error:', error);
      const friendlyMessage = mapNhostError(error);
      throw new Error(friendlyMessage);
    }

    console.log('[AuthService] Verification email sent successfully');
    return { success: true };
  } catch (err) {
    console.error('[AuthService] Unexpected error sending verification email:', err);
    throw new Error(err.message || 'Failed to send verification email. Please try again.');
  }
}

/**
 * Reset password
 * @param {string} email - Email address to send reset link to
 * @returns {Promise<{ success: boolean, error?: object }>} Result object
 */
export async function resetPassword(email) {
  try {
    if (!email) {
      throw new Error('Email address is required');
    }

    const { error } = await nhost.auth.resetPassword({
      email: email.trim(),
    });

    if (error) {
      console.error('[AuthService] Reset password error:', error);
      const friendlyMessage = mapNhostError(error);
      throw new Error(friendlyMessage);
    }

    console.log('[AuthService] Password reset email sent successfully');
    return { success: true };
  } catch (err) {
    console.error('[AuthService] Unexpected error during password reset:', err);
    throw new Error(err.message || 'Failed to send password reset email. Please try again.');
  }
}

/**
 * Check verification status by refreshing session
 * @returns {Promise<{ isVerified: boolean, user?: object }>} Verification status
 */
export async function checkVerificationStatus() {
  try {
    await nhost.auth.refreshSession();
    const user = getCurrentUser();
    const isVerified = user?.emailVerified === true;

    if (isVerified) {
      clearPendingVerificationEmail();
    }

    return { isVerified, user };
  } catch (err) {
    console.error('[AuthService] Error checking verification status:', err);
    return { isVerified: false };
  }
}
