// src/services/creditService.js
import { nhost } from './nhost'; // Assuming nhost is configured
import { ensureValidSession } from './auth'; // Import session validation
import { gql } from '@apollo/client'; // Assuming @apollo/client is installed
import axios from 'axios';

// --- Custom Error Classes ---

class CreditServiceError extends Error {
  /**
   * @param {string} message Error message
   * @param {unknown} [originalError] The original error caught, if any
   */
  constructor(message, originalError) {
    super(message);
    this.name = 'CreditServiceError';
    this.originalError = originalError;
  }
}

class WalletNotFoundError extends CreditServiceError {
  /**
   * @param {string} userId The ID of the user whose wallet was not found
   */
  constructor(userId) {
    super(`Wallet not found for user ID: ${userId}`);
    this.name = 'WalletNotFoundError';
  }
}

class InsufficientCreditsError extends CreditServiceError {
  /**
   * @param {string} message Error message
   * @param {number} required Required credits
   * @param {number} available Available credits
   */
  constructor(message, required, available) {
    super(message);
    this.name = 'InsufficientCreditsError';
    this.required = required;
    this.available = available;
  }
}

class GraphQLServiceError extends CreditServiceError {
  /**
   * @param {string} message Error message
   * @param {readonly any[]} [graphqlErrors] Array of GraphQL errors from response
   * @param {unknown} [originalError] The original error caught, if any
   */
  constructor(message, graphqlErrors, originalError) {
    // Extract message from the first GraphQL error if available
    const gqlMessage = graphqlErrors?.[0]?.message || message;
    super(`GraphQL operation failed: ${gqlMessage}`, originalError);
    this.name = 'GraphQLServiceError';
    // Store the raw GraphQL errors array if needed
    this.graphqlErrors = graphqlErrors;
  }
}

// --- Type Definitions (using JSDoc for documentation) ---

/**
 * @typedef {object} PassType
 * @property {string} id
 * @property {string} name
 * @property {'free' | 'paid' | 'subscription'} type
 * @property {number} credits_per_day
 * @property {number} max_hours
 * @property {boolean} is_unlimited
 * @property {number} price
 * @property {object} features
 * @property {string} created_at
 * @property {string} updated_at
 */

/**
 * @typedef {object} UserPass
 * @property {string} id
 * @property {string} user_id
 * @property {string} pass_type_id
 * @property {string} start_date
 * @property {string} end_date
 * @property {'active' | 'expired' | 'cancelled'} status
 * @property {string} created_at
 * @property {string} updated_at
 * @property {PassType} [pass_type]
 */

/**
 * @typedef {object} UserWallet
 * @property {string} user_id
 * @property {number} credits_balance
 * @property {number} fast_credits_balance
 * @property {string|null} last_free_credit_grant
 * @property {string|null} active_pass_id
 * @property {string} created_at
 * @property {string} updated_at
 */

/**
 * @typedef {object} UserWalletInfo
 * @property {number} credits - User's regular credits balance
 * @property {number} fastCredits - User's fast credits balance
 * @property {boolean} hasUnlimitedUsage - Whether user has unlimited usage from a pass
 * @property {string|null} lastFreeCreditDate - Date of last free credit allocation
 * @property {UserPass|null} activePass - User's active pass details
 * @property {boolean} canClaimDailyCredits - Whether user can claim daily credits
 */

/**
 * @typedef {object} CreditTransaction
 * @property {string} id
 * @property {string} user_id
 * @property {number} amount
 * @property {string} transaction_type
 * @property {number} balance_after
 * @property {string|null} reference_id
 * @property {string|null} bundle_id
 * @property {string} created_at
 */

/**
 * @typedef {object} CreditBundle
 * @property {string} id
 * @property {string} name
 * @property {number} credits
 * @property {number} price
 * @property {number} duration_days
 * @property {boolean} is_active
 * @property {string} created_at
 * @property {string} updated_at
 */

/**
 * @typedef {object} DeductCreditsPayload
 * @property {string} userId
 * @property {number} amount
 * @property {'credit_usage'} transactionType
 * @property {string} [referenceId]
 * @property {'regular'|'fast'} deductionSource
 */

/**
 * @typedef {object} CreditTransactionResponse
 * @property {boolean} success
 * @property {string} message
 * @property {string} [transactionId]
 * @property {number} [balanceAfter]
 * @property {number} [fastBalanceAfter]
 * @property {boolean} [hasUnlimitedUsage]
 */

// --- GraphQL Queries ---

const GET_USER_WALLET_WITH_PASS = gql`
  query GetUserWalletWithPass($userId: uuid!) {
    user_wallet(where: { user_id: { _eq: $userId } }) {
      user_id
      credits_balance
      fast_credits_balance
      last_free_credit_grant
      active_pass_id
      created_at
      updated_at
    }
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      hours_used
      created_at
      updated_at
      pass_type {
        id
        name
        tier
        category
        monthly_price
        hours_included
        is_unlimited
        slow_generation_limit
        fast_generation_limit
        features
        created_at
        updated_at
      }
    }
  }
`;

const GET_USER_PASSES = gql`
  query GetUserPasses($userId: uuid!) {
    user_passes(
      where: {
        user_id: { _eq: $userId },
        status: { _eq: "active" },
        _or: [
          { end_date: { _is_null: true } },
          { end_date: { _gte: "now()" } }
        ]
      }
    ) {
      id
      user_id
      pass_type_id
      start_date
      end_date
      status
      hours_used
      created_at
      updated_at
      pass_type {
        id
        name
        tier
        category
        monthly_price
        hours_included
        is_unlimited
        slow_generation_limit
        fast_generation_limit
        features
        created_at
        updated_at
      }
    }
  }
`;

const GET_CREDIT_BUNDLES = gql`
  query GetCreditBundles {
    credit_bundles(where: { is_active: { _eq: true } }, order_by: { sort_order: asc }) {
      id
      name
      description
      price
      credits_amount
      is_featured
      is_active
      sort_order
      created_at
      updated_at
    }
  }
`;

const GET_CREDIT_TRANSACTIONS = gql`
  query GetCreditTransactions($userId: uuid!, $limit: Int = 50) {
    credit_transactions(
      where: { user_id: { _eq: $userId } },
      order_by: { created_at: desc },
      limit: $limit
    ) {
      id
      user_id
      change_amount
      balance_after
      fast_balance_after
      deducted_from
      reason
      related_id
      notes
      created_at
    }
  }
`;

const GET_CREDIT_TRANSACTIONS = gql`
  query GetCreditTransactions($userId: uuid!, $limit: Int) {
    credit_transactions(
      where: { user_id: { _eq: $userId } },
      order_by: { created_at: desc },
      limit: $limit
    ) {
      id
      user_id
      amount
      transaction_type
      balance_after
      reference_id
      bundle_id
      created_at
    }
  }
`;

// --- Service Implementation ---

/**
 * Fetches the user's wallet information including active pass details
 *
 * @param {string} userId - The UUID of the user whose wallet is being fetched.
 * @returns {Promise<UserWalletInfo>} A promise resolving to the user's wallet information.
 * @throws {CreditServiceError | WalletNotFoundError | GraphQLServiceError} - Throws specific errors on failure.
 */
async function getUserWalletInfo(userId) {
  // Validate input
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    throw new CreditServiceError("Invalid or missing User ID provided.");
  }

  console.log(`[CreditService] 💰 Fetching wallet info for user ${userId} using nhost.graphql.request`);

  try {
    // Ensure we have a valid session before making the request
    const sessionResult = await ensureValidSession();
    if (!sessionResult.success) {
      throw new GraphQLServiceError('Authentication required', [], sessionResult.error);
    }

    // Fetch wallet data with active pass information
    const { data, error } = await nhost.graphql.request(
      GET_USER_WALLET_WITH_PASS,
      { userId }
    );

    // Handle GraphQL specific errors
    if (error) {
      console.error(`[CreditService] ❌ GraphQL request error for user ${userId}:`, error);
      throw new GraphQLServiceError(error.message, error.response?.errors, error);
    }

    // Handle case where wallet data is not found
    if (!data?.user_wallet || data.user_wallet.length === 0) {
      console.warn(`[CreditService] ⚠️ Wallet data not found for user ${userId}`);
      throw new WalletNotFoundError(userId);
    }

    /** @type {UserWallet} */
    const walletData = data.user_wallet[0];

    // Find active pass from the separate user_passes query
    const activePasses = data.user_passes || [];
    const activePass = activePasses.find(pass =>
      pass.status === 'active' &&
      (!pass.end_date || new Date(pass.end_date) > new Date())
    );

    // Check if user can claim daily credits
    const canClaimDailyCredits = isEligibleForDailyCredits(walletData.last_free_credit_grant);

    // Check if user has unlimited usage from active pass
    const hasActivePass = !!activePass;
    const hasUnlimitedUsage = hasActivePass &&
                             activePass?.pass_type?.is_unlimited === true;

    // Log the fetched wallet info for debugging
    console.log(`[CreditService] ✅ User ${userId} Wallet Info retrieved:`, {
      user_id: walletData.user_id,
      credits_balance: walletData.credits_balance,
      fast_credits_balance: walletData.fast_credits_balance,
      last_free_credit_grant: walletData.last_free_credit_grant,
      active_pass_id: walletData.active_pass_id,
      has_active_pass: hasActivePass,
      has_unlimited_usage: hasUnlimitedUsage,
      can_claim_daily_credits: canClaimDailyCredits,
      active_pass_details: activePass
    });

    // Map to the desired return structure, providing defaults and using camelCase
    const walletInfo = {
      credits: walletData.credits_balance ?? 0,
      fastCredits: walletData.fast_credits_balance ?? 0,
      hasUnlimitedUsage,
      lastFreeCreditDate: walletData.last_free_credit_grant ?? null,
      activePass: activePass ?? null,
      canClaimDailyCredits
    };

    return walletInfo;

  } catch (error) {
    // Catch and handle specific known error types first
    if (error instanceof CreditServiceError || 
        error instanceof WalletNotFoundError || 
        error instanceof GraphQLServiceError) {
      throw error; // Re-throw known service errors
    } else {
      // Handle unexpected errors
      console.error(`[CreditService] 💥 Unexpected error fetching wallet for ${userId}:`, error);
      const message = error instanceof Error ? error.message : 'An unknown error occurred during wallet fetch';
      throw new CreditServiceError(message, error);
    }
  }
}

/**
 * Checks if a user is eligible to receive daily credits
 * @param {string|null} lastFreeCreditDate - The date of the last free credit allocation
 * @returns {boolean} Whether the user is eligible for daily credits
 */
function isEligibleForDailyCredits(lastFreeCreditDate) {
  if (!lastFreeCreditDate) {
    return true;
  }

  const lastDate = new Date(lastFreeCreditDate);
  
  // Check if the last credit date was yesterday or earlier
  lastDate.setHours(0, 0, 0, 0);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return lastDate < today;
}

/**
 * Claims daily credits for a user
 * @param {string} userId - The user ID
 * @returns {Promise<CreditTransactionResponse>} The result of the daily credit claim
 * @throws {CreditServiceError} If the claim fails
 */
async function claimDailyCredits(userId) {
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    throw new CreditServiceError("Invalid or missing User ID provided.");
  }

  console.log(`[CreditService] 🎁 Claiming daily credits for user ${userId}`);

  try {
    // Call the Nhost function for daily credit distribution
    const { data, error } = await nhost.functions.call(
      'credit/daily',
      { userId }
    );

    if (error) {
      console.error(`[CreditService] ❌ Daily credit claim error:`, error);
      throw new CreditServiceError(`Failed to claim daily credits: ${error.message}`);
    }

    if (!data || !data.success) {
      const errorMessage = data?.message || 'Unknown error during daily credit claim';
      console.error(`[CreditService] ❌ Daily credit claim failed:`, errorMessage);
      throw new CreditServiceError(errorMessage);
    }

    console.log(`[CreditService] ✅ Daily credits claimed successfully:`, data);
    return data;
  } catch (error) {
    if (error instanceof CreditServiceError) {
      throw error;
    }
    console.error(`[CreditService] 💥 Unexpected error claiming daily credits:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new CreditServiceError(message, error);
  }
}

/**
 * Deducts credits for a usage transaction
 * @param {number} amount - The amount of credits to deduct (positive value)
 * @param {string} referenceId - ID of the related entity (prompt, project, etc.)
 * @param {'regular'|'fast'} deductionSource - Source of credits to deduct from
 * @returns {Promise<CreditTransactionResponse>} The result of the credit deduction
 * @throws {CreditServiceError|InsufficientCreditsError} If the deduction fails
 */
async function deductCredits(amount, referenceId, deductionSource = 'regular') {
  // Validate input
  if (typeof amount !== 'number' || !Number.isFinite(amount) || amount <= 0) {
    throw new CreditServiceError("Invalid deduction amount: Must be a positive number.");
  }
  
  if (!referenceId || typeof referenceId !== 'string' || referenceId.trim() === '') {
    throw new CreditServiceError("A valid reference ID is required for credit deduction.");
  }
  
  if (!['regular', 'fast'].includes(deductionSource)) {
    throw new CreditServiceError("Invalid deduction source. Must be 'regular' or 'fast'.");
  }

  console.log(`[CreditService] 💸 Deducting ${amount} credits from ${deductionSource} for reference ${referenceId}`);

  try {
    // Create transaction request
    const transactionRequest = {
      userId: nhost.auth.getUser()?.id,
      amount: -amount, // Negative for deductions
      transactionType: 'credit_usage',
      referenceId,
      deductionSource
    };

    // Call the credit transaction handler
    const { data, error } = await nhost.functions.call(
      'credit/transaction',
      transactionRequest
    );

    if (error) {
      console.error(`[CreditService] ❌ Credit deduction error:`, error);
      throw new CreditServiceError(`Failed to deduct credits: ${error.message}`);
    }

    if (!data || !data.success) {
      const errorMessage = data?.message || 'Unknown error during credit deduction';
      console.error(`[CreditService] ❌ Credit deduction failed:`, errorMessage);
      
      // Check for insufficient balance error
      if (data?.type === 'INSUFFICIENT_BALANCE') {
        throw new InsufficientCreditsError(
          errorMessage,
          amount,
          deductionSource === 'fast' ? data.fastBalanceAfter : data.balanceAfter
        );
      }
      
      throw new CreditServiceError(errorMessage);
    }

    console.log(`[CreditService] ✅ Credits deducted successfully:`, data);
    return data;
  } catch (error) {
    if (error instanceof CreditServiceError) {
      throw error;
    }
    console.error(`[CreditService] 💥 Unexpected error deducting credits:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new CreditServiceError(message, error);
  }
}

/**
 * Fetches available credit bundles for purchase
 * @returns {Promise<CreditBundle[]>} List of available credit bundles
 * @throws {CreditServiceError} If the fetch fails
 */
async function getCreditBundles() {
  console.log(`[CreditService] 📦 Fetching available credit bundles`);

  try {
    const { data, error } = await nhost.graphql.request(GET_CREDIT_BUNDLES);

    if (error) {
      console.error(`[CreditService] ❌ Error fetching credit bundles:`, error);
      throw new GraphQLServiceError(error.message, error.response?.errors, error);
    }

    if (!data?.credit_bundles) {
      console.warn(`[CreditService] ⚠️ No credit bundles found`);
      return [];
    }

    console.log(`[CreditService] ✅ Found ${data.credit_bundles.length} credit bundles`);
    return data.credit_bundles;
  } catch (error) {
    if (error instanceof CreditServiceError) {
      throw error;
    }
    console.error(`[CreditService] 💥 Unexpected error fetching credit bundles:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new CreditServiceError(message, error);
  }
}

/**
 * Purchases a credit bundle
 * @param {string} bundleId - ID of the bundle to purchase
 * @returns {Promise<CreditTransactionResponse>} The result of the purchase
 * @throws {CreditServiceError} If the purchase fails
 */
async function purchaseBundle(bundleId) {
  if (!bundleId || typeof bundleId !== 'string' || bundleId.trim() === '') {
    throw new CreditServiceError("Invalid or missing bundle ID.");
  }

  console.log(`[CreditService] 🛒 Purchasing credit bundle ${bundleId}`);

  try {
    // This function should connect to your payment processor
    // For this implementation, we'll assume the payment is already handled
    // and we're just adding the credits to the user's account
    
    const transactionRequest = {
      userId: nhost.auth.getUser()?.id,
      bundleId,
      transactionType: 'credit_purchase'
    };

    // Call the credit transaction handler
    const { data, error } = await nhost.functions.call(
      'credit/transaction',
      transactionRequest
    );

    if (error) {
      console.error(`[CreditService] ❌ Bundle purchase error:`, error);
      throw new CreditServiceError(`Failed to purchase bundle: ${error.message}`);
    }

    if (!data || !data.success) {
      const errorMessage = data?.message || 'Unknown error during bundle purchase';
      console.error(`[CreditService] ❌ Bundle purchase failed:`, errorMessage);
      throw new CreditServiceError(errorMessage);
    }

    console.log(`[CreditService] ✅ Bundle purchased successfully:`, data);
    return data;
  } catch (error) {
    if (error instanceof CreditServiceError) {
      throw error;
    }
    console.error(`[CreditService] 💥 Unexpected error purchasing bundle:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new CreditServiceError(message, error);
  }
}

/**
 * Gets user's credit transaction history
 * @param {string} userId - User ID
 * @param {number} [limit=20] - Maximum number of transactions to return
 * @returns {Promise<CreditTransaction[]>} List of credit transactions
 * @throws {CreditServiceError} If the fetch fails
 */
async function getCreditTransactions(userId, limit = 20) {
  if (!userId || typeof userId !== 'string' || userId.trim() === '') {
    throw new CreditServiceError("Invalid or missing User ID provided.");
  }

  console.log(`[CreditService] 📜 Fetching credit transactions for user ${userId}, limit ${limit}`);

  try {
    const { data, error } = await nhost.graphql.request(
      GET_CREDIT_TRANSACTIONS,
      { userId, limit }
    );

    if (error) {
      console.error(`[CreditService] ❌ Error fetching credit transactions:`, error);
      throw new GraphQLServiceError(error.message, error.response?.errors, error);
    }

    if (!data?.credit_transactions) {
      console.warn(`[CreditService] ⚠️ No credit transactions found`);
      return [];
    }

    console.log(`[CreditService] ✅ Found ${data.credit_transactions.length} credit transactions`);
    return data.credit_transactions;
  } catch (error) {
    if (error instanceof CreditServiceError) {
      throw error;
    }
    console.error(`[CreditService] 💥 Unexpected error fetching credit transactions:`, error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    throw new CreditServiceError(message, error);
  }
}

// --- Export Service ---

export const CreditService = {
  // Wallet and pass management
  getUserWalletInfo,
  isEligibleForDailyCredits,
  
  // Credit operations
  claimDailyCredits,
  deductCredits,
  
  // Bundle operations
  getCreditBundles,
  purchaseBundle,
  
  // History
  getCreditTransactions,
  
  // Error classes
  CreditServiceError,
  WalletNotFoundError,
  InsufficientCreditsError,
  GraphQLServiceError
};
